#!/usr/bin/env python3
"""
Database initialization script for StoreDiscount API
This script creates the necessary database tables.
"""

import sys
import os
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import create_tables, DATABASE_URL

def init_database():
    """Initialize the database with all necessary tables"""
    try:
        print("🔄 Initializing database...")
        print(f"📍 Database URL: {DATABASE_URL}")
        
        # Create tables
        create_tables()
        
        print("✅ Database initialized successfully!")
        print("📋 Created tables:")
        print("   - users")
        
        return True
        
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure MySQL server is running")
        print("2. Check database credentials in .env file")
        print("3. Ensure database exists")
        print("4. Verify user has proper permissions")
        return False

def test_connection():
    """Test database connection"""
    try:
        load_dotenv()
        
        DB_HOST = os.getenv("DB_HOST", "localhost")
        DB_USER = os.getenv("DB_USER", "root")
        DB_PASS = os.getenv("DB_PASS", "")
        DB_NAME = os.getenv("DB_NAME", "default_db")
        
        print("🔍 Testing database connection...")
        print(f"   Host: {DB_HOST}")
        print(f"   User: {DB_USER}")
        print(f"   Database: {DB_NAME}")
        
        # Test connection
        engine = create_engine(DATABASE_URL)
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("✅ Database connection successful!")
            return True
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 StoreDiscount Database Initialization")
    print("=" * 50)
    
    # Test connection first
    if test_connection():
        # Initialize database
        if init_database():
            print("\n🎉 Setup complete! You can now start the FastAPI server.")
        else:
            sys.exit(1)
    else:
        print("\n💡 Please ensure:")
        print("1. MySQL server is running")
        print("2. Database 'carlos' exists")
        print("3. Credentials in .env are correct")
        sys.exit(1)
