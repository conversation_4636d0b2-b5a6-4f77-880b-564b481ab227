#!/usr/bin/env python3
"""
Script to test favorites functionality
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_favorites():
    """Test the favorites functionality"""
    
    # First, let's register/login a test user
    print("1. Testing user registration/login...")
    
    user_data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        # Register or login
        response = requests.post(f"{BASE_URL}/auth/register-or-login", json=user_data)
        if response.status_code == 200:
            auth_data = response.json()
            token = auth_data["access_token"]
            print(f"✅ User authenticated successfully")
            
            headers = {"Authorization": f"Bearer {token}"}
            
            # Test getting discount products (should include is_favorite field)
            print("\n2. Testing discount products with favorite status...")
            response = requests.get(f"{BASE_URL}/products/discounts", headers=headers)
            if response.status_code == 200:
                products = response.json()
                print(f"✅ Got {len(products)} discount products")
                if products:
                    first_product = products[0]
                    print(f"First product: {first_product['name']} (is_favorite: {first_product.get('is_favorite', 'N/A')})")
                    
                    # Test adding to favorites
                    print(f"\n3. Testing adding product {first_product['id']} to favorites...")
                    favorite_data = {"product_id": first_product["id"]}
                    response = requests.post(f"{BASE_URL}/favorites/toggle", json=favorite_data, headers=headers)
                    if response.status_code == 200:
                        result = response.json()
                        print(f"✅ Toggle result: {result}")
                        
                        # Test getting favorites
                        print("\n4. Testing get favorites...")
                        response = requests.get(f"{BASE_URL}/favorites", headers=headers)
                        if response.status_code == 200:
                            favorites = response.json()
                            print(f"✅ Got {len(favorites)} favorites")
                            for fav in favorites:
                                print(f"  - {fav['product']['name']} at {fav['product']['store']}")
                        else:
                            print(f"❌ Failed to get favorites: {response.status_code} - {response.text}")
                        
                        # Test removing from favorites
                        print(f"\n5. Testing removing product {first_product['id']} from favorites...")
                        response = requests.post(f"{BASE_URL}/favorites/toggle", json=favorite_data, headers=headers)
                        if response.status_code == 200:
                            result = response.json()
                            print(f"✅ Toggle result: {result}")
                        else:
                            print(f"❌ Failed to remove favorite: {response.status_code} - {response.text}")
                    else:
                        print(f"❌ Failed to add favorite: {response.status_code} - {response.text}")
                else:
                    print("❌ No products found")
            else:
                print(f"❌ Failed to get products: {response.status_code} - {response.text}")
        else:
            print(f"❌ Failed to authenticate: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🧪 Testing favorites functionality...")
    test_favorites()
    print("\n🎉 Test completed!")
