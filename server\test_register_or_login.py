#!/usr/bin/env python3
"""
Test script for the new register-or-login endpoint
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_register_or_login():
    """Test the register-or-login endpoint"""
    print("🧪 Testing register-or-login endpoint...")
    
    test_user = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    print(f"\n1️⃣ First attempt - should create new account")
    try:
        response = requests.post(
            f"{BASE_URL}/auth/register-or-login",
            headers={"Content-Type": "application/json"},
            json=test_user
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ First attempt successful (account created)")
            print(f"   Token type: {data['token_type']}")
            print(f"   Token: {data['access_token'][:50]}...")
            first_token = data['access_token']
        else:
            print(f"❌ First attempt failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ First attempt error: {e}")
        return False
    
    print(f"\n2️⃣ Second attempt - should login with existing account")
    try:
        response = requests.post(
            f"{BASE_URL}/auth/register-or-login",
            headers={"Content-Type": "application/json"},
            json=test_user
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Second attempt successful (logged in)")
            print(f"   Token type: {data['token_type']}")
            print(f"   Token: {data['access_token'][:50]}...")
            second_token = data['access_token']
            
            # Tokens should be different (new session)
            if first_token != second_token:
                print("✅ Different tokens generated (as expected)")
            else:
                print("⚠️  Same token returned (might be cached)")
                
        else:
            print(f"❌ Second attempt failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Second attempt error: {e}")
        return False
    
    print(f"\n3️⃣ Third attempt - wrong password should fail")
    wrong_password_user = {
        "email": "<EMAIL>",
        "password": "wrongpassword"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/register-or-login",
            headers={"Content-Type": "application/json"},
            json=wrong_password_user
        )
        
        if response.status_code == 401:
            print("✅ Third attempt correctly failed with wrong password")
            error_data = response.json()
            print(f"   Error message: {error_data.get('detail', 'No detail')}")
        else:
            print(f"❌ Third attempt should have failed but got: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Third attempt error: {e}")
        return False
    
    print(f"\n✅ All tests passed! The register-or-login endpoint works correctly.")
    return True

if __name__ == "__main__":
    test_register_or_login()
