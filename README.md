# Store Discount App

A mobile application for tracking store discounts with user authentication, product listings, and favorites functionality.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Project Structure](#project-structure)
- [Backend Setup](#backend-setup)
  - [Database Configuration](#database-configuration)
  - [Installing Dependencies](#installing-dependencies)
  - [Running the Server](#running-the-server)
- [Frontend Setup](#frontend-setup)
  - [Installing Dependencies](#installing-dependencies-1)
  - [Running the Client](#running-the-client)
- [Development Environment](#development-environment)
  - [Quick Start Scripts](#quick-start-scripts)
  - [Manual Setup](#manual-setup)
- [API Documentation](#api-documentation)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### Backend Requirements
- Python 3.11 or higher
- MySQL Server
- All dependencies listed in `server/pyproject.toml`

### Frontend Requirements
- Node.js and npm
- Expo CLI (will be installed automatically if using npm start)

## Project Structure

```
store_app/
├── client/              # React Native frontend
├── server/              # FastAPI backend
├── cron/                # Cron jobs for data scraping
├── start-dev.bat        # Windows batch script
├── start-dev.ps1        # Windows PowerShell script
└── start-dev.sh         # Unix/Linux/macOS shell script
```

## Backend Setup

### Database Configuration

1. Install MySQL Server:
   - **Windows**: Download MySQL Installer from [mysql.com](https://dev.mysql.com/downloads/installer/)
   - **macOS**: `brew install mysql`
   - **Linux**: `sudo apt install mysql-server`

2. Start MySQL Service:
   - **Windows**: Start "MySQL80" service or run `net start mysql80`
   - **macOS**: `brew services start mysql`
   - **Linux**: `sudo systemctl start mysql`

3. Create Database:
   ```bash
   mysql -u root -p
   ```
   Enter your MySQL root password, then run:
   ```sql
   CREATE DATABASE carlos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   SHOW DATABASES;
   EXIT;
   ```

4. Configure Environment Variables:
   Copy `server/.env.example` to `server/.env` and update the values:
   ```env
   DB_HOST=localhost
   DB_USER=root
   DB_PASS=your_password
   DB_NAME=carlos
   SECRET_KEY=your-super-secret-key-change-in-production-please
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   ```

### Installing Dependencies

Navigate to the server directory and install dependencies using uv:
```bash
cd server
# Install uv if you haven't already
pip install uv
# Install project dependencies
uv sync
```

### Initializing Database Tables

After configuring the database, initialize the tables:
```bash
cd server
uv run python init_db.py
```

### Running the Server

For development:
```bash
cd server
uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

The server will be available at:
- API: http://localhost:8000
- Documentation: http://localhost:8000/docs

## Frontend Setup

### Installing Dependencies

Navigate to the client directory and install dependencies:
```bash
cd client
npm install
```

### Running the Client

Start the Expo development server:
```bash
cd client
npm start
```

This will open Expo DevTools in your browser and provide options to:
- Run on Android emulator/device
- Run on iOS simulator
- Run on web browser

## Development Environment

### Quick Start Scripts

Three scripts are provided for different environments:

#### Windows (Batch File)
```bash
start-dev.bat
```
- Opens two separate command prompt windows
- One for the FastAPI server (Python)
- One for the Expo client (React Native)

#### Windows (PowerShell)
```powershell
.\start-dev.ps1
```
- Opens two separate PowerShell windows
- Provides colored output for better visibility
- Same functionality as the batch file

#### Unix/Linux/macOS (Shell Script)
```bash
./start-dev.sh
```
- Runs both services in the background
- Provides process IDs for monitoring
- Use Ctrl+C to stop both services cleanly

### Manual Setup

If you prefer to run services manually:

#### Terminal 1 - Server
```bash
cd server
uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### Terminal 2 - Client
```bash
cd client
npm start
```

## API Documentation

Once the server is running, you can access the interactive API documentation at:
- http://localhost:8000/docs (Swagger UI)
- http://localhost:8000/redoc (ReDoc)

## Troubleshooting

- **Server fails to start**: Check that Python dependencies are installed and MySQL is configured correctly
- **Client fails to start**: Ensure Node.js and npm are installed, run `npm install` in the client directory
- **Database connection errors**: Verify MySQL is running and credentials in `server/.env` are correct
- **Port conflicts**: The server uses port 8000, ensure it's not already in use
- **Missing dependencies**: Run `uv sync` in the server directory and `npm install` in the client directory

For more detailed MySQL setup instructions, see [MYSQL_SETUP.md](server/MYSQL_SETUP.md).