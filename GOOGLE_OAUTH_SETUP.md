# Google OAuth Implementation Guide

This guide explains how to set up and use Google OAuth authentication in the StoreDiscount app.

## 🎯 What's Implemented

✅ **Backend Google OAuth Support**
- Google ID token verification
- User creation/login with Google accounts
- Account linking (existing email users can link Google accounts)
- Database schema updated with Google OAuth fields

✅ **Frontend Google OAuth Integration**
- React Native Google Sign-In using expo-auth-session
- AuthContext updated with googleLogin method
- UI integration in LoginScreen

✅ **Database Migration**
- Added Google OAuth fields to users table
- Made password field nullable for OAuth users
- Added proper indexes and constraints

## 🔧 Setup Instructions

### 1. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Configure OAuth consent screen
6. Create credentials for:
   - **Web application** (for backend verification)
   - **iOS application** (if deploying to iOS)
   - **Android application** (if deploying to Android)

### 2. Backend Configuration

1. Copy `server/.env.example` to `server/.env`
2. Update the Google Client ID:
   ```env
   GOOGLE_CLIENT_ID=your-web-client-id-here.apps.googleusercontent.com
   ```

### 3. Frontend Configuration

1. Update `client/config/google.ts` with your actual client IDs:
   ```typescript
   export const GOOGLE_CONFIG = {
     WEB_CLIENT_ID: 'your-web-client-id.apps.googleusercontent.com',
     IOS_CLIENT_ID: 'your-ios-client-id.apps.googleusercontent.com',
     ANDROID_CLIENT_ID: 'your-android-client-id.apps.googleusercontent.com',
   };
   ```

2. Update the app scheme in `client/screens/LoginScreen.tsx`:
   ```typescript
   redirectUri: AuthSession.makeRedirectUri({
     scheme: 'your-app-scheme', // Replace with your actual app scheme
   }),
   ```

### 4. Database Migration

The database has been automatically updated with the migration script. If you need to run it manually:

```bash
cd server
uv run python migrate_google_oauth.py
```

## 📱 How It Works

### User Flow

1. **New Google User**: Creates account automatically with Google profile info
2. **Existing Email User**: Links Google account to existing email account
3. **Returning Google User**: Logs in directly with Google account

### Database Schema

The `users` table now includes:
- `google_id` - Unique Google user identifier
- `provider` - Authentication provider ('email', 'google')
- `first_name` - User's first name from Google
- `last_name` - User's last name from Google
- `profile_picture_url` - Google profile picture URL
- `hashed_password` - Now nullable for OAuth users

### API Endpoints

- `POST /auth/google` - Authenticate with Google ID token
- `POST /auth/register` - Traditional email/password registration
- `POST /auth/login` - Traditional email/password login
- `GET /auth/me` - Get current user info

## 🧪 Testing

Run the test suite to verify everything is working:

```bash
cd server
uv run python test_google_auth.py
```

### Test Results
- ✅ Health Check: Server is running
- ✅ Google Auth Endpoint: Properly rejects invalid tokens
- ✅ Regular Auth: Email/password authentication still works

## 🔒 Security Features

- **Token Verification**: Google ID tokens are verified server-side
- **Account Linking**: Prevents duplicate accounts for same email
- **Provider Tracking**: Tracks authentication method for each user
- **Secure Storage**: Tokens stored securely using AsyncStorage

## 📝 Usage Examples

### Frontend (React Native)

```typescript
// In a component
const { googleLogin, isLoading, error } = useAuth();

const handleGoogleSignIn = async () => {
  try {
    await promptAsync(); // Triggers Google OAuth flow
    // handleGoogleAuthSuccess will be called automatically
  } catch (error) {
    console.error('Google sign-in failed:', error);
  }
};
```

### Backend (FastAPI)

```python
# Google OAuth endpoint
@app.post("/auth/google", response_model=Token)
async def google_auth(google_token: dict, db: Session = Depends(get_db)):
    google_user_info = await verify_google_token(google_token.get("token"))
    # Handle user creation/login logic
    return {"access_token": access_token, "token_type": "bearer"}
```

## 🚀 Next Steps

1. **Configure actual Google Client IDs** in production
2. **Set up proper app schemes** for mobile deployment
3. **Test on actual devices** (Google OAuth works best on real devices)
4. **Add error handling** for network issues
5. **Implement logout** functionality
6. **Add profile management** features

## 🐛 Troubleshooting

### Common Issues

1. **"Google OAuth not configured"**: Check GOOGLE_CLIENT_ID in .env
2. **"Invalid token"**: Verify client ID matches Google Console
3. **Database errors**: Run migration script
4. **Web demo limitations**: Full functionality requires mobile device

### Debug Mode

Enable debug logging in the backend by setting log level to DEBUG in main.py.

## 📚 References

- [Expo AuthSession Documentation](https://docs.expo.dev/versions/latest/sdk/auth-session/)
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [FastAPI Security Documentation](https://fastapi.tiangolo.com/tutorial/security/)
