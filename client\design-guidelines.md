# StoreDiscount App Design Guidelines

## Overview
StoreDiscount is a mobile application that helps users find and compare discounted prices across different stores. The app features a clean, modern interface with a focus on readability and ease of use.

## Brand Identity

### Logo and App Name
- App name: StoreDiscount
- Display: Bold, prominent typography
- Positioning: Centered at the top of key screens

### Color Palette
- Primary: Black (#000000)
- Secondary: White (#FFFFFF)
- Background: Light gray (#F5F5F5) for content areas
- Accent: Blue for interactive elements (buttons, links)
- Text: Dark gray for body text, black for headers

## Typography

### Fonts
- Headers: Sans-serif, bold weight
- Body text: Sans-serif, regular weight
- Input fields: Light gray placeholder text

### Text Hierarchy
- Page titles: 20-24pt
- Section headers: 18pt
- List items: 16pt
- Supporting text: 14pt
- Timestamps and secondary info: 12pt

## UI Components

### Buttons
- Primary buttons: Black background with white text, rounded corners
- Social login buttons: Light gray background with brand logo and text
- Icon buttons: Simple, recognizable icons with optional labels

### Input Fields
- Rounded corners with light border
- Clear placeholder text
- Sufficient padding for touch targets

### Cards and Lists
- White background with subtle shadow
- Consistent padding (16px)
- Star icon for favorites/bookmarking
- Clear visual hierarchy for product information

### Navigation
- Bottom tab bar with 5 key sections:
  - Home
  - Calendar/Schedule
  - Shopping/Bag
  - Favorites/Saved
  - Profile/Account
- Icon + text label format

## Screen-Specific Guidelines

### Sign In / Authentication
- Clean, minimalist design
- Multiple sign-in options (email, Google, Apple)
- Clear call-to-action buttons
- Visible terms and privacy policy links

### Product Listing
- Search bar at the top
- Filter/sort options readily available
- Consistent product card format:
  - Product name
  - Price comparison (original - discounted)
  - Store name
  - Last updated timestamp
  - Favorite/bookmark option

### Navigation Patterns
- Top navigation for filters and views (Favorites, Today, Last Day)
- Bottom navigation for main app sections
- Back button for nested screens

## Accessibility Guidelines
- Maintain sufficient color contrast (minimum 4.5:1 for normal text)
- Touch targets minimum size of 44x44 pixels
- Support for screen readers
- Scalable text for users with visual impairments

## Responsive Design
- Support for various iOS and Android device sizes
- Consistent padding and margins across screen sizes
- Flexible layouts that adapt to different orientations

## Animation and Transitions
- Subtle, purposeful animations
- Standard navigation transitions
- Loading states for data retrieval operations

## Implementation Notes
- Use platform-native components where possible
- Ensure consistent rendering across iOS and Android
- Follow platform-specific design guidelines where appropriate

## Asset Specifications
- Icons: SVG or PNG format, multiple resolutions for different device densities
- Images: Optimized for mobile, appropriate compression
- App icon: Follow platform guidelines for iOS and Android

This document serves as a reference for maintaining design consistency across the StoreDiscount application. All new features and UI components should adhere to these guidelines.