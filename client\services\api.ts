const API_BASE_URL = 'http://192.168.68.103:8000';

export interface User {
  id: number;
  email: string;
  is_active: boolean;
  provider?: string;
  first_name?: string;
  last_name?: string;
  profile_picture_url?: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
}

export interface UserCreateRequest {
  email: string;
  password: string;
}

export interface UserLoginRequest {
  email: string;
  password: string;
}

export interface Product {
  id: number;
  name: string;
  store: string;
  price: number;
  price_discount?: number;
  discount_percentage?: number;
  last_update: string;
  created_at: string;
  discount_start?: string;
  discount_end?: string;
  is_favorite?: boolean;
}

export interface ProductCreateRequest {
  name: string;
  store: string;
  price: number;
  price_discount?: number;
  discount_percentage?: number;
}

export interface Favorite {
  id: number;
  user_id: number;
  product_id: number;
  created_at: string;
  product: Product;
}

export interface FavoriteToggleRequest {
  product_id: number;
}

export interface FavoriteToggleResponse {
  is_favorite: boolean;
  message: string;
}

class ApiService {
  private baseUrl: string;
  private token: string | null = null;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  setToken(token: string) {
    this.token = token;
  }

  clearToken() {
    this.token = null;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const config: RequestInit = {
      ...options,
      headers,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Network error occurred');
    }
  }

  // Authentication endpoints
  async register(userData: UserCreateRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async login(credentials: UserLoginRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async registerOrLogin(userData: UserCreateRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>('/auth/register-or-login', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getCurrentUser(): Promise<User> {
    return this.request<User>('/auth/me');
  }

  async googleAuth(token: string): Promise<AuthResponse> {
    return this.request<AuthResponse>('/auth/google', {
      method: 'POST',
      body: JSON.stringify({ token }),
    });
  }

  // Health check
  async healthCheck(): Promise<{ message: string }> {
    return this.request<{ message: string }>('/');
  }

  // Product endpoints
  async getDiscountProducts(
    search?: string,
    favorites_only?: boolean,
    today_only?: boolean,
    last_day_only?: boolean
  ): Promise<Product[]> {
    const params = new URLSearchParams();
    
    if (search) params.append('search', search);
    if (favorites_only !== undefined) params.append('favorites_only', favorites_only.toString());
    if (today_only !== undefined) params.append('today_only', today_only.toString());
    if (last_day_only !== undefined) params.append('last_day_only', last_day_only.toString());
    
    const queryString = params.toString();
    return this.request<Product[]>(`/products/discounts${queryString ? `?${queryString}` : ''}`);
  }

  async getAllProducts(search?: string, page: number = 1, limit: number = 20): Promise<Product[]> {
    const params = new URLSearchParams();
    if (search) params.append('search', search);
    params.append('page', page.toString());
    params.append('limit', limit.toString());

    const queryString = params.toString();
    return this.request<Product[]>(`/products${queryString ? `?${queryString}` : ''}`);
  }

  async createProduct(productData: ProductCreateRequest): Promise<Product> {
    return this.request<Product>('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    });
  }

  // Favorites endpoints
  async toggleFavorite(productId: number): Promise<FavoriteToggleResponse> {
    return this.request<FavoriteToggleResponse>('/favorites/toggle', {
      method: 'POST',
      body: JSON.stringify({ product_id: productId }),
    });
  }

  async getFavorites(): Promise<Favorite[]> {
    return this.request<Favorite[]>('/favorites');
  }

  async checkFavoriteStatus(productId: number): Promise<{ is_favorite: boolean }> {
    return this.request<{ is_favorite: boolean }>(`/favorites/check/${productId}`);
  }
}

export const apiService = new ApiService();
