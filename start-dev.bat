@echo off
echo Starting Store App Development Environment...
echo.

REM Start the server in a new command prompt window
echo Starting FastAPI server...
start "FastAPI Server" cmd /k "cd server && python main.py"

REM Wait a moment for the server to start
timeout /t 3 /nobreak >nul

REM Start the client in a new command prompt window
echo Starting Expo client...
start "Expo Client" cmd /k "cd client && npm start"

echo.
echo Both services are starting...
echo - Server: http://localhost:8000
echo - Client: Will open Expo DevTools
echo.
echo Press any key to exit this script (services will continue running)
pause >nul