import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { apiService, Product } from '../services/api';

export default function CalendarScreen() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [showTodayOnly, setShowTodayOnly] = useState(false);
  const [showLastDayOnly, setShowLastDayOnly] = useState(false);

  useEffect(() => {
    loadDiscountProducts();
  }, [searchQuery, showFavoritesOnly, showTodayOnly, showLastDayOnly]);

  const loadDiscountProducts = async () => {
    try {
      setLoading(true);
      const discountProducts = await apiService.getDiscountProducts(
        searchQuery || undefined, // search parameter
        showFavoritesOnly, // favorites_only parameter
        showTodayOnly, // today_only parameter
        showLastDayOnly // last_day_only parameter
      );
      setProducts(discountProducts);
    } catch (error) {
      console.error('Error loading discount products:', error);
      Alert.alert('Error', 'Failed to load discount products. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return price.toFixed(2);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleToggleFavorite = async (productId: number) => {
    try {
      const response = await apiService.toggleFavorite(productId);

      // Update the local state to reflect the change
      setProducts(prevProducts =>
        prevProducts.map(product =>
          product.id === productId
            ? { ...product, is_favorite: response.is_favorite }
            : product
        )
      );

      // Show a brief message (optional)
      // Alert.alert('Success', response.message);
    } catch (error) {
      console.error('Error toggling favorite:', error);
      Alert.alert('Error', 'Failed to update favorite. Please try again.');
    }
  };

  const renderProductItem = (product: Product) => (
    <View key={product.id} style={styles.productCard}>
      <View style={styles.productHeader}>
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={() => handleToggleFavorite(product.id)}
        >
          <Ionicons
            name={product.is_favorite ? "star" : "star-outline"}
            size={20}
            color={product.is_favorite ? "#FFD700" : "#6B7280"}
          />
        </TouchableOpacity>
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{product.name}</Text>
          <Text style={styles.storeName}>{product.store}</Text>
          <Text style={styles.lastUpdate}>
            - Last Update{' '}{formatDate(product.last_update)}
          </Text>
        </View>
        <View style={styles.priceContainer}>
          <Text style={styles.priceText}>
            {formatPrice(product.price)} - {formatPrice(product.price_discount || 0)}
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <View style={styles.content}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#6B7280" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search"
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
        </View>

        {/* Filter Buttons */}
        <View style={styles.filterContainer}>
          <TouchableOpacity
            style={[styles.filterButton, showFavoritesOnly && styles.filterButtonActive]}
            onPress={() => setShowFavoritesOnly(!showFavoritesOnly)}
          >
            <Ionicons
              name={showFavoritesOnly ? "heart" : "heart-outline"}
              size={16}
              color={showFavoritesOnly ? "#FFFFFF" : "#6B7280"}
            />
            <Text style={[styles.filterText, showFavoritesOnly && styles.filterTextActive]}>
              Favorites
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterButton, showTodayOnly && styles.filterButtonActive]}
            onPress={() => setShowTodayOnly(!showTodayOnly)}
          >
            <Ionicons
              name="time-outline"
              size={16}
              color={showTodayOnly ? "#FFFFFF" : "#6B7280"}
            />
            <Text style={[styles.filterText, showTodayOnly && styles.filterTextActive]}>
              Today
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterButton, showLastDayOnly && styles.filterButtonActive]}
            onPress={() => setShowLastDayOnly(!showLastDayOnly)}
          >
            <Ionicons
              name="calendar-outline"
              size={16}
              color={showLastDayOnly ? "#FFFFFF" : "#6B7280"}
            />
            <Text style={[styles.filterText, showLastDayOnly && styles.filterTextActive]}>
              Last Day
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.filterButton}>
            <Ionicons name="grid-outline" size={16} color="#6B7280" />
          </TouchableOpacity>
        </View>

        {/* Products List */}
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Stores</Text>
            <Text style={styles.sectionSubtitle}>Discount prices</Text>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#007AFF" />
              <Text style={styles.loadingText}>Loading discount products...</Text>
            </View>
          ) : products.length > 0 ? (
            products.map(renderProductItem)
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                {searchQuery ? 'No products found matching your search.' : 'No discount products available.'}
              </Text>
            </View>
          )}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 60,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#000000',
  },
  filterContainer: {
    flexDirection: 'row',
    marginBottom: 24,
    paddingHorizontal: 0,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    // marginRight: 12,
    // backgroundColor: '#FFFFFF',
    borderRadius: 20,
    // borderWidth: 1,
    // borderColor: '#E5E5E5',
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 1,
    // },
    // shadowOpacity: 0.1,
    // shadowRadius: 2,
    // elevation: 2,
  },
  filterText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 6,
    fontWeight: '500',
  },
  filterButtonActive: {
    backgroundColor: '#000000',
    borderColor: '#000000',
  },
  filterTextActive: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  sectionHeader: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
  },
  productCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  favoriteButton: {
    marginRight: 12,
    marginTop: 2,
  },
  productInfo: {
    flex: 1,
    marginRight: 12,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  storeName: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  lastUpdate: {
    fontSize: 12,
    color: '#9CA3AF',
    lineHeight: 16,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  priceText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
});
