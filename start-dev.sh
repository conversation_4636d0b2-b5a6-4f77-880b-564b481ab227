#!/bin/bash

echo "Starting Store App Development Environment..."
echo ""

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if required commands exist
if ! command_exists python; then
    echo "Error: Python is not installed or not in PATH"
    exit 1
fi

if ! command_exists npm; then
    echo "Error: npm is not installed or not in PATH"
    exit 1
fi

# Start the server in the background
echo "Starting FastAPI server..."
cd server
python main.py &
SERVER_PID=$!
cd ..

# Wait a moment for the server to start
sleep 3

# Start the client
echo "Starting Expo client..."
cd client
npm start &
CLIENT_PID=$!
cd ..

echo ""
echo "Both services are starting..."
echo "- Server: http://localhost:8000 (PID: $SERVER_PID)"
echo "- Client: Will open Expo DevTools (PID: $CLIENT_PID)"
echo ""
echo "Press Ctrl+C to stop both services"

# Function to cleanup processes on exit
cleanup() {
    echo ""
    echo "Stopping services..."
    kill $SERVER_PID 2>/dev/null
    kill $CLIENT_PID 2>/dev/null
    echo "Services stopped."
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop the script
wait