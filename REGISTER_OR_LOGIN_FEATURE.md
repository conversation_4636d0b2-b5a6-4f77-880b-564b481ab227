# Register or Login Feature

## Overview

The StoreDiscount app now supports a unified authentication flow where users can enter their email and password, and the system will automatically:

1. **Create a new account** if the email is not registered
2. **Login to existing account** if the email is already registered with the correct password
3. **Show an error** if the email is registered but the password is incorrect

This provides a seamless user experience where users don't need to remember whether they've already created an account.

## Implementation Details

### Backend Changes

#### New Endpoint: `/auth/register-or-login`

- **Method**: POST
- **Body**: `{"email": "<EMAIL>", "password": "password123"}`
- **Response**: `{"access_token": "jwt_token", "token_type": "bearer"}`

**Behavior**:
- If email doesn't exist: Creates new account and returns token
- If email exists with correct password: Logs in and returns token  
- If email exists with wrong password: Returns 401 error

#### Code Location
- **Endpoint**: `server/main.py` - `register_or_login()` function
- **API Service**: `client/services/api.ts` - `registerOrLogin()` method

### Frontend Changes

#### AuthContext Updates
- **File**: `client/contexts/AuthContext.tsx`
- **New Method**: `registerOrLogin(email: string, password: string)`
- **Usage**: Same interface as existing `register()` and `login()` methods

#### LoginScreen Updates
- **File**: `client/screens/LoginScreen.tsx`
- **Changes**:
  - Updated to use `registerOrLogin` instead of `register`
  - Changed UI text from "Create an account" to "Sign in or create account"
  - Updated description to reflect dual functionality
  - Modified success message to be more generic

#### Web Demo Updates
- **File**: `client/web-demo.html`
- **Changes**:
  - Updated to use new `/auth/register-or-login` endpoint
  - Updated UI text to match mobile app
  - Modified success message

## User Experience

### Before
1. User enters email and password
2. If account exists: Error "Email already registered"
3. User has to remember if they have an account or try login separately

### After
1. User enters email and password
2. System automatically handles registration or login
3. User gets signed in regardless of whether account existed
4. Only error case is wrong password for existing account

## Error Handling

The system provides clear error messages:

- **New account created**: "Welcome! You have been signed in successfully!"
- **Existing account login**: "Welcome! You have been signed in successfully!"
- **Wrong password**: "Incorrect password for existing account"
- **Invalid email**: "Please enter a valid email address"
- **Network error**: Appropriate network error message

## Testing

### Automated Tests

1. **Backend Test**: `server/test_register_or_login.py`
   - Tests account creation on first attempt
   - Tests login on second attempt with same credentials
   - Tests error handling with wrong password

2. **Integration Test**: Updated `server/test_api.py`
   - Includes register-or-login endpoint in full test suite
   - Verifies token generation and authentication

### Manual Testing

1. **Web Demo**: Open `client/web-demo.html` in browser
2. **Mobile App**: Run with Expo Go
3. **Test Cases**:
   - Enter new email/password → Should create account
   - Enter same email/password → Should login
   - Enter same email/wrong password → Should show error

## Security Considerations

- Password validation remains the same (minimum 6 characters)
- JWT tokens are generated fresh for each authentication
- Existing password hashing and verification unchanged
- No additional security risks introduced

## Backward Compatibility

- Original `/auth/register` and `/auth/login` endpoints remain unchanged
- Existing authentication flows continue to work
- No breaking changes to existing functionality

## Future Enhancements

Potential improvements for this feature:
1. Add "Forgot Password" flow for wrong password scenarios
2. Implement account linking for social logins
3. Add email verification for new accounts
4. Implement rate limiting for failed attempts
