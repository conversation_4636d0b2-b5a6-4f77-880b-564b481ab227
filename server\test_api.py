#!/usr/bin/env python3
"""
Test script for StoreDiscount API
Tests user registration, login, and protected endpoints
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_user_registration():
    """Test user registration"""
    print("\n🔍 Testing user registration...")
    
    test_user = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/register",
            headers={"Content-Type": "application/json"},
            json=test_user
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ User registration successful")
            print(f"   Token type: {data['token_type']}")
            print(f"   Token: {data['access_token'][:50]}...")
            return data['access_token']
        elif response.status_code == 400:
            print("⚠️  User already exists (this is expected if running multiple times)")
            # Try to login instead
            return test_user_login()
        else:
            print(f"❌ Registration failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return None

def test_user_login():
    """Test user login"""
    print("\n🔍 Testing user login...")
    
    test_user = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/login",
            headers={"Content-Type": "application/json"},
            json=test_user
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ User login successful")
            print(f"   Token type: {data['token_type']}")
            print(f"   Token: {data['access_token'][:50]}...")
            return data['access_token']
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_protected_endpoint(token):
    """Test protected endpoint with token"""
    print("\n🔍 Testing protected endpoint...")
    
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Protected endpoint access successful")
            print(f"   User ID: {data['id']}")
            print(f"   Email: {data['email']}")
            print(f"   Active: {data['is_active']}")
            print(f"   Created: {data['created_at']}")
            return True
        else:
            print(f"❌ Protected endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Protected endpoint error: {e}")
        return False

def test_register_or_login():
    """Test the new register-or-login endpoint"""
    print("\n🔍 Testing register-or-login endpoint...")

    test_user = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }

    try:
        # First attempt - should create account
        response = requests.post(
            f"{BASE_URL}/auth/register-or-login",
            headers={"Content-Type": "application/json"},
            json=test_user
        )

        if response.status_code == 200:
            data = response.json()
            print("✅ Register-or-login (new account) successful")
            print(f"   Token type: {data['token_type']}")
            print(f"   Token: {data['access_token'][:50]}...")

            # Second attempt - should login
            response2 = requests.post(
                f"{BASE_URL}/auth/register-or-login",
                headers={"Content-Type": "application/json"},
                json=test_user
            )

            if response2.status_code == 200:
                data2 = response2.json()
                print("✅ Register-or-login (existing account) successful")
                print(f"   Token type: {data2['token_type']}")
                print(f"   Token: {data2['access_token'][:50]}...")
                return data2['access_token']
            else:
                print(f"❌ Second attempt failed: {response2.status_code}")
                return None
        else:
            print(f"❌ Register-or-login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Register-or-login error: {e}")
        return None

def main():
    """Run all tests"""
    print("🚀 StoreDiscount API Test Suite")
    print("=" * 50)

    # Test health check
    if not test_health_check():
        print("\n❌ Health check failed. Make sure the server is running.")
        sys.exit(1)

    # Test registration
    token = test_user_registration()
    if not token:
        print("\n❌ Registration failed. Cannot continue with tests.")
        sys.exit(1)

    # Test new register-or-login endpoint
    combined_token = test_register_or_login()
    if not combined_token:
        print("\n❌ Register-or-login test failed.")
        sys.exit(1)

    # Test protected endpoint
    if test_protected_endpoint(token):
        print("\n🎉 All tests passed! MySQL integration is working correctly.")
    else:
        print("\n❌ Protected endpoint test failed.")
        sys.exit(1)

    print("\n📊 Test Summary:")
    print("✅ Health check")
    print("✅ User registration/login")
    print("✅ Register-or-login endpoint")
    print("✅ JWT token authentication")
    print("✅ Protected endpoint access")
    print("✅ MySQL database integration")

if __name__ == "__main__":
    main()
