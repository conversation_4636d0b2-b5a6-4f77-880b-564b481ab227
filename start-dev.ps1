# Store App Development Environment Starter
Write-Host "Starting Store App Development Environment..." -ForegroundColor Green
Write-Host ""

# Start the server in a new PowerShell window
Write-Host "Starting FastAPI server..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd server; python main.py" -WindowStyle Normal

# Wait a moment for the server to start
Start-Sleep -Seconds 3

# Start the client in a new PowerShell window
Write-Host "Starting Expo client..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd client; npm start" -WindowStyle Normal

Write-Host ""
Write-Host "Both services are starting..." -ForegroundColor Green
Write-Host "- Server: http://localhost:8000" -ForegroundColor Cyan
Write-Host "- Client: Will open Expo DevTools" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to exit this script (services will continue running)" -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")