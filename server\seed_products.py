#!/usr/bin/env python3
"""
Script to seed the products table with sample discount data
"""

from sqlalchemy.orm import Session
from database import SessionLocal, Product, create_tables
from datetime import datetime, timedelta
import random

def create_sample_products():
    """Create sample products with discounts"""
    
    # Sample products data based on the image
    sample_products = [
        {
            "name": "<PERSON><PERSON><PERSON> (Rice)",
            "store": "HyperMaxi",
            "price": 201.75,
            "price_discount": 172.34,
        },
        {
            "name": "Cereal",
            "store": "Fidalga",
            "price": 201.75,
            "price_discount": 172.34,
        },
        {
            "name": "Manzana",
            "store": "HiperMaxi",
            "price": 201.75,
            "price_discount": 172.34,
        },
        {
            "name": "Lavadora",
            "store": "Fidalga",
            "price": 3000.0,
            "price_discount": 2990.0,
        },
        {
            "name": "Tostadora",
            "store": "HiperMaxi",
            "price": 1200.0,
            "price_discount": 1100.0,
        },
        # Additional sample products
        {
            "name": "Leche",
            "store": "SuperMarket",
            "price": 85.50,
            "price_discount": 75.00,
        },
        {
            "name": "Pan",
            "store": "Panadería Central",
            "price": 45.00,
            "price_discount": 40.00,
        },
        {
            "name": "Pollo",
            "store": "Carnicería López",
            "price": 180.00,
            "price_discount": 160.00,
        },
        {
            "name": "Televisor 55\"",
            "store": "ElectroMax",
            "price": 15000.0,
            "price_discount": 13500.0,
        },
        {
            "name": "Refrigerador",
            "store": "HyperMaxi",
            "price": 25000.0,
            "price_discount": 22000.0,
        }
    ]
    
    db = SessionLocal()
    try:
        # Clear existing products (optional)
        print("Clearing existing products...")
        db.query(Product).delete()
        
        print("Creating sample products...")
        for product_data in sample_products:
            # Calculate discount percentage
            discount_percentage = None
            if product_data["price_discount"]:
                discount_percentage = ((product_data["price"] - product_data["price_discount"]) / product_data["price"]) * 100
            
            # Create random last_update times (within last 7 days)
            last_update = datetime.utcnow() - timedelta(days=random.randint(0, 7), hours=random.randint(0, 23))
            
            product = Product(
                name=product_data["name"],
                store=product_data["store"],
                price=product_data["price"],
                price_discount=product_data["price_discount"],
                discount_percentage=discount_percentage,
                last_update=last_update
            )
            
            db.add(product)
            print(f"Added: {product.name} at {product.store} - ${product.price} -> ${product.price_discount}")
        
        db.commit()
        print(f"\n✅ Successfully created {len(sample_products)} sample products!")
        
    except Exception as e:
        print(f"❌ Error creating sample products: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🌱 Seeding products database...")
    
    # Ensure tables exist
    try:
        create_tables()
    except Exception as e:
        print(f"⚠️  Database connection issue: {e}")
        print("Make sure MySQL is running and database 'carlos' exists")
        exit(1)
    
    create_sample_products()
    print("🎉 Database seeding completed!")
