import React from 'react';
import { Platform } from 'react-native';
import LoginScreen from './screens/LoginScreen';

// Simple web-compatible version for testing
export default function App() {
  if (Platform.OS === 'web') {
    // For web, just show the login screen without navigation
    return <LoginScreen />;
  }

  // For mobile, use the full app with navigation
  const { View, ActivityIndicator, StyleSheet } = require('react-native');
  const { NavigationContainer } = require('@react-navigation/native');
  const { AuthProvider, useAuth } = require('./contexts/AuthContext');
  const TabNavigator = require('./navigation/TabNavigator').default;

  function AppContent() {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#000000" />
        </View>
      );
    }

    return (
      <NavigationContainer>
        {isAuthenticated ? <TabNavigator /> : <LoginScreen />}
      </NavigationContainer>
    );
  }

  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

const styles = {
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
};
