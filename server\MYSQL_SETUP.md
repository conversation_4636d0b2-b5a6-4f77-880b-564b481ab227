# MySQL Setup Guide for StoreDiscount API

## 🚀 Quick Setup

### 1. Install MySQL Server

**Windows:**
- Download MySQL Installer from [mysql.com](https://dev.mysql.com/downloads/installer/)
- Run installer and choose "Developer Default"
- Set root password to `qazwsx` (or update `.env` file)

**macOS:**
```bash
brew install mysql
brew services start mysql
mysql_secure_installation
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

### 2. Start MySQL Service

**Windows:**
- Open Services (services.msc)
- Find "MySQL80" service and start it
- Or use Command Prompt as Administrator:
```cmd
net start mysql80
```

**macOS/Linux:**
```bash
sudo systemctl start mysql
# or
brew services start mysql
```

### 3. Create Database

Connect to MySQL and create the database:

```bash
mysql -u root -p
```

Enter password: `qazwsx`

Then run:
```sql
CREATE DATABASE carlos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
SHOW DATABASES;
EXIT;
```

### 4. Initialize Database Tables

Run the initialization script:
```bash
cd server
uv run python init_db.py
```

### 5. Start the API Server

```bash
uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 🔧 Configuration

The database configuration is in `server/.env`:

```env
SECRET_KEY=your-super-secret-key-change-in-production-please
# DATABASE_URL=sqlite:///./store_discount.db
DB_HOST=localhost
DB_USER=root
DB_PASS=
DB_NAME=default_db
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

## 🧪 Testing the Setup

### Test Database Connection
```bash
uv run python init_db.py
```

### Test API Endpoints

1. **Health Check:**
```bash
curl http://localhost:8000/
```

2. **Register User:**
```bash
curl -X POST http://localhost:8000/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

3. **Login User:**
```bash
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

## 📊 Database Schema

### Users Table
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email)
);
```

## 🔍 Troubleshooting

### Common Issues:

1. **Connection Refused (Error 2003)**
   - MySQL server is not running
   - Check if service is started
   - Verify port 3306 is not blocked

2. **Access Denied (Error 1045)**
   - Wrong username/password
   - Check `.env` file credentials
   - Reset MySQL root password if needed

3. **Database Doesn't Exist (Error 1049)**
   - Run the database creation SQL
   - Check database name in `.env`

4. **Permission Denied**
   - Grant proper privileges to user
   - Run as administrator/sudo if needed

### Reset MySQL Root Password:

**Windows:**
```cmd
mysqld --skip-grant-tables
mysql -u root
USE mysql;
UPDATE user SET authentication_string=PASSWORD('qazwsx') WHERE User='root';
FLUSH PRIVILEGES;
EXIT;
```

**macOS/Linux:**
```bash
sudo mysql
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'qazwsx';
FLUSH PRIVILEGES;
EXIT;
```

## 🎯 Next Steps

Once MySQL is set up:

1. ✅ Database tables created
2. ✅ API server running
3. ✅ Test user registration/login
4. 🔄 Connect React Native app
5. 🚀 Start building features!

## 📞 Support

If you encounter issues:
1. Check MySQL error logs
2. Verify `.env` configuration
3. Test connection with `init_db.py`
4. Restart MySQL service
5. Check firewall settings
