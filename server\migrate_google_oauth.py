#!/usr/bin/env python3
"""
Migration script to add Google OAuth fields to the users table
"""
import os
from dotenv import load_dotenv
import pymysql

# Load environment variables
load_dotenv()

# Database configuration
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_USER = os.getenv("DB_USER", "root")
DB_PASS = os.getenv("DB_PASS", "")
DB_NAME = os.getenv("DB_NAME", "default_db")

def run_migration():
    """Add Google OAuth fields to users table"""
    try:
        # Connect to database
        connection = pymysql.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASS,
            database=DB_NAME,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            print("🔄 Adding Google OAuth fields to users table...")
            
            # Check if columns already exist
            cursor.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'users' 
                AND COLUMN_NAME IN ('google_id', 'provider', 'profile_picture_url', 'first_name', 'last_name')
            """, (DB_NAME,))
            
            existing_columns = [row[0] for row in cursor.fetchall()]
            
            # Add missing columns
            migrations = [
                ("google_id", "ALTER TABLE users ADD COLUMN google_id VARCHAR(255) UNIQUE NULL"),
                ("provider", "ALTER TABLE users ADD COLUMN provider VARCHAR(50) NULL"),
                ("profile_picture_url", "ALTER TABLE users ADD COLUMN profile_picture_url VARCHAR(500) NULL"),
                ("first_name", "ALTER TABLE users ADD COLUMN first_name VARCHAR(100) NULL"),
                ("last_name", "ALTER TABLE users ADD COLUMN last_name VARCHAR(100) NULL"),
            ]
            
            for column_name, sql in migrations:
                if column_name not in existing_columns:
                    print(f"   Adding column: {column_name}")
                    cursor.execute(sql)
                else:
                    print(f"   Column {column_name} already exists, skipping")
            
            # Make hashed_password nullable for OAuth users
            print("   Making hashed_password nullable...")
            cursor.execute("ALTER TABLE users MODIFY COLUMN hashed_password VARCHAR(255) NULL")
            
            # Add index on google_id
            print("   Adding index on google_id...")
            try:
                cursor.execute("CREATE INDEX idx_users_google_id ON users(google_id)")
            except pymysql.err.OperationalError as e:
                if "Duplicate key name" in str(e):
                    print("   Index on google_id already exists, skipping")
                else:
                    raise
            
            connection.commit()
            print("✅ Migration completed successfully!")
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        raise
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    print("🚀 Google OAuth Migration")
    print("=" * 40)
    run_migration()
    print("🎉 Migration complete!")
