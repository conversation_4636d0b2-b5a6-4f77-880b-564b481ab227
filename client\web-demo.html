<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StoreDiscount - Login Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #F5F5F5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 24px;
        }

        .container {
            width: 100%;
            max-width: 400px;
        }

        .title {
            font-size: 24px;
            font-weight: bold;
            color: #000000;
            text-align: center;
            margin-bottom: 60px;
        }

        .form-container {
            background-color: #FFFFFF;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            font-size: 20px;
            font-weight: bold;
            color: #000000;
            text-align: center;
            margin-bottom: 8px;
        }

        .description {
            font-size: 14px;
            color: #6B7280;
            text-align: center;
            margin-bottom: 32px;
        }

        .input {
            width: 100%;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            padding: 16px;
            font-size: 16px;
            background-color: #FFFFFF;
            margin-bottom: 16px;
        }

        .input::placeholder {
            color: #9CA3AF;
        }

        .error-text {
            color: #EF4444;
            font-size: 14px;
            text-align: center;
            margin-bottom: 16px;
            display: none;
        }

        .continue-button {
            width: 100%;
            background-color: #000000;
            border: none;
            border-radius: 8px;
            padding: 16px;
            color: #FFFFFF;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 24px;
        }

        .continue-button:hover {
            background-color: #333333;
        }

        .continue-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .divider-container {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }

        .divider-line {
            flex: 1;
            height: 1px;
            background-color: #E5E7EB;
        }

        .divider-text {
            color: #9CA3AF;
            font-size: 14px;
            margin: 0 16px;
        }

        .social-button {
            width: 100%;
            background-color: #F9FAFB;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            padding: 16px;
            color: #374151;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            margin-bottom: 12px;
        }

        .social-button:hover {
            background-color: #F3F4F6;
        }

        .terms-text {
            font-size: 12px;
            color: #9CA3AF;
            text-align: center;
            margin-top: 16px;
            line-height: 18px;
        }

        .link-text {
            color: #3B82F6;
            text-decoration: underline;
        }

        .demo-note {
            background-color: #FEF3C7;
            border: 1px solid #F59E0B;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 24px;
            text-align: center;
            font-size: 14px;
            color: #92400E;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="title">StoreDiscount</h1>

        <div class="form-container">
            <div class="demo-note">
                🚀 <strong>Web Demo Version</strong><br>
                Full functionality available on mobile with Expo Go app
            </div>

            <h2 class="subtitle">Sign in or create account</h2>
            <p class="description">Enter your email and password to sign in or create a new account</p>

            <form id="loginForm">
                <input type="email" class="input" id="email" placeholder="<EMAIL>" required>

                <input type="password" class="input" id="password" placeholder="Password (min 6 characters)" required
                    minlength="6">

                <div class="error-text" id="errorText"></div>

                <button type="submit" class="continue-button" id="continueBtn">
                    Continue
                </button>
            </form>

            <div class="divider-container">
                <div class="divider-line"></div>
                <span class="divider-text">or</span>
                <div class="divider-line"></div>
            </div>

            <button class="social-button" onclick="showComingSoon('Google')">
                🔍 Continue with Google
            </button>

            <button class="social-button" onclick="showComingSoon('Apple')">
                🍎 Continue with Apple
            </button>

            <p class="terms-text">
                By clicking continue, you agree to our
                <span class="link-text">Terms of Service</span>
                and
                <span class="link-text">Privacy Policy</span>
            </p>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorText = document.getElementById('errorText');
            const continueBtn = document.getElementById('continueBtn');

            // Clear previous errors
            errorText.style.display = 'none';

            // Basic validation
            if (!email || !password) {
                showError('Please fill in all fields');
                return;
            }

            if (password.length < 6) {
                showError('Password must be at least 6 characters long');
                return;
            }

            // Show loading state
            continueBtn.disabled = true;
            continueBtn.textContent = 'Loading...';

            try {
                // Demo API call to the FastAPI backend
                const response = await fetch('http://localhost:8000/auth/register-or-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    alert('Success! You have been signed in successfully!\n\nToken: ' + data.access_token.substring(0, 50) + '...');
                } else {
                    const errorData = await response.json();
                    showError(errorData.detail || 'Authentication failed');
                }
            } catch (error) {
                showError('Network error. Make sure the FastAPI server is running on localhost:8000');
            } finally {
                // Reset button state
                continueBtn.disabled = false;
                continueBtn.textContent = 'Continue';
            }
        });

        function showError(message) {
            const errorText = document.getElementById('errorText');
            errorText.textContent = message;
            errorText.style.display = 'block';
        }

        function showComingSoon(provider) {
            alert(`${provider} Sign-In coming soon!\n\nThis is a demo version. Full functionality available on mobile.`);
        }
    </script>
</body>

</html>