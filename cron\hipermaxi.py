import requests
import mysql.connector
from mysql.connector import Error
import time
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

base_url = 'https://www.hipermaxi.com'

def get_collections():
    return [
        {"id": 1, "name": "<PERSON>barrote<PERSON>"}, 
        {"id": 2, "name": "Bebidas"},
        {"id": 3, "name": "Carnes"},
        {"id": 8, "name": "Congelados"},
        {"id": 4, "name": "Fiambres"},
        {"id": 7, "name": "Lácteos y Derivados"},
        {"id": 5, "name": "Panadería"},
        {"id": 6, "name": "Pastelería y Masas Típicas"},
        {"id": 11, "name": "Cuidado del Bebé"},
        {"id": 9, "name": "Cuidado del Hogar"},
        {"id": 10, "name": "Cuidado Personal"},
        {"id": 34, "name": "Bazar Importación"},
        {"id": 13, "name": "Bazar"},
        {"id": 36, "name": "Juguetería Importación"},
        {"id": 21, "name": "Juguetería"},
        {"id": 15, "name": "Farmacia Éticos"},
        {"id": 16, "name": "Farmacia Genéricos"},
        {"id": 14, "name": "Farmacia Otc"},
        {"id": 12, "name": "Frutas y Verduras"},
        {"id": 33, "name": "Granos y Hortalizas"},
    ]

def scrape_and_save(collection):
    """
    Scrapes product data from JSON API and saves it to a MySQL database.
    """
    db_config = {
        'host': os.getenv('DB_HOST'),
        'user': os.getenv('DB_USER'),
        'password': os.getenv('DB_PASSWORD'),
        'database': os.getenv('DB_NAME')
    }
    
    conn = None
    cursor = None

    try:
        # Database Connection
        print("Connecting to MySQL database...")
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        print("Database connection successful.")

        # Create Table (if it doesn't exist)
        table_name = 'products'
        create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id INT AUTO_INCREMENT PRIMARY KEY,
            store VARCHAR(255) NOT NULL,
            category VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            description VARCHAR(1000),
            price DECIMAL(10, 2) NOT NULL,
            price_discount DECIMAL(10, 2),
            product_url VARCHAR(2083),
            image_url VARCHAR(2083),
            last_update DATETIME,
            discount_start DATETIME,
            discount_end DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_product (store, category, name)
        );
        """
        cursor.execute(create_table_query)
        print(f"Table '{table_name}' created or already exists.")

        # JSON API Scraping with Pagination
        category = collection['name']
        page_number = 1
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        total_products_scraped = 0

        while True:
            current_url = f'https://hipermaxi.com/tienda-api/api/v1/public/productos?IdMarket=67&IdLocatario=67&IdCategoria={collection["id"]}&Pagina={page_number}&Cantidad=50'
            print(f"\n--- Scraping Page {page_number}: {current_url} ---")
            
            response = requests.get(current_url, headers=headers)
            response.raise_for_status()

            # Parse JSON response
            data = response.json()
            products = data.get('Dato', [])

            if not products or len(products) == 0:
                print("No products found on this page. Ending scrape.")
                break

            print(f"Found {len(products)} products on this page. Extracting data...")
            product_data = []

            for product in products:
                try:
                    name = product.get('Descripcion', 'N/A')
                    description = product.get('Descripcion', '')
                    productId = product.get('IdProducto', None)

                    # Handle price - using the correct field names from API
                    price = float(product.get('PrecioVenta', 0)) if product.get('PrecioVenta') else 0.0
                    if product.get('ConOferta'):
                        price = float(product.get('PrecioOriginal'))
                    price_discount = float(product.get('PrecioOferta', 0)) if product.get('PrecioOferta') and product.get('ConOferta') else None

                    # Handle URLs - using the correct field names from API
                    product_url = f'https://www.hipermaxi.com/santa-cruz/hipermaxi-roca-y-coronado/producto/{productId}/{description.lower().replace(' ', '-')}'
                    
                    image_url = product.get('UrlFoto', '')
                    if image_url and image_url.startswith('//'):
                        image_url = 'https:' + image_url
                    elif image_url and not image_url.startswith('http') and image_url:
                        image_url = base_url + image_url

                    product_data.append(('Hipermaxi', category, name, description, price, price_discount, product_url, image_url))
                    total_products_scraped += 1

                except Exception as e:
                    print(f"Error extracting data for a product: {e}")

            # Insert Data for the current page
            if product_data:
                insert_query = f"""
                    INSERT INTO {table_name} (store, category, name, description, price, price_discount, product_url, image_url, discount_start, discount_end) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, {'NOW()' if price_discount else 'NULL'}, NULL) 
                    ON DUPLICATE KEY UPDATE 
                        price = VALUES(price), 
                        price_discount = VALUES(price_discount), 
                        image_url = VALUES(image_url), 
                        discount_start = CASE WHEN price_discount IS NOT NULL AND discount_start IS NULL THEN NOW() ELSE discount_start END, 
                        discount_end = VALUES(discount_end),
                        last_update = NOW()"""
                cursor.executemany(insert_query, product_data)
                conn.commit()
                print(f"{cursor.rowcount} records inserted/updated for this page.")
            
            page_number += 1
            time.sleep(1)  # Be respectful with API requests

        print(f"\n--- Scraping Complete ---")
        print(f"Total products scraped: {total_products_scraped}")

    except requests.exceptions.RequestException as e:
        print(f"Error fetching the URL: {e}")
    except Error as e:
        print(f"Error connecting to MySQL: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        if conn and conn.is_connected():
            if cursor:
                cursor.close()
            conn.close()
            print("MySQL connection is closed.")

if __name__ == '__main__':
    collections = get_collections()
    for collection in collections:
        scrape_and_save(collection)

