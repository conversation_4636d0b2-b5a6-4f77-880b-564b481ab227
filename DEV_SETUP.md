# Development Environment Setup

This project contains scripts to easily start both the client and server simultaneously.

## Prerequisites

### Server Requirements
- Python 3.11 or higher
- All dependencies listed in `server/pyproject.toml`

### Client Requirements
- Node.js and npm
- Expo CLI (will be installed automatically if using npm start)

## Quick Start Scripts

Three scripts are provided for different environments:

### Windows (Batch File)
```bash
start-dev.bat
```
- Opens two separate command prompt windows
- One for the FastAPI server (Python)
- One for the Expo client (React Native)

### Windows (PowerShell)
```powershell
.\start-dev.ps1
```
- Opens two separate PowerShell windows
- Provides colored output for better visibility
- Same functionality as the batch file

### Unix/Linux/macOS (Shell Script)
```bash
./start-dev.sh
```
- Runs both services in the background
- Provides process IDs for monitoring
- Use Ctrl+C to stop both services cleanly

## What Each Script Does

1. **Starts the FastAPI Server**
   - Changes to the `server/` directory
   - Runs `python main.py`
   - Server will be available at `http://localhost:8000`

2. **Starts the Expo Client**
   - Changes to the `client/` directory  
   - Runs `npm start`
   - Opens Expo DevTools in your browser
   - Provides QR code for mobile testing

## Manual Setup (Alternative)

If you prefer to run services manually:

### Terminal 1 - Server
```bash
cd server
python main.py
```

### Terminal 2 - Client
```bash
cd client
npm start
```

## Troubleshooting

- **Server fails to start**: Check that Python dependencies are installed and MySQL is configured
- **Client fails to start**: Ensure Node.js and npm are installed, run `npm install` in the client directory
- **Port conflicts**: The server uses port 8000, ensure it's not already in use

## Development URLs

- **API Server**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Expo DevTools**: Will open automatically in your browser