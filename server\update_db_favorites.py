#!/usr/bin/env python3
"""
Script to update database with favorites table
"""

from sqlalchemy.orm import Session
from sqlalchemy import text
from database import SessionLocal, engine, Base, UserFavorite
import pymysql

def create_favorites_table():
    """Create the user_favorites table"""
    db = SessionLocal()
    try:
        print("Creating user_favorites table...")
        
        # Create the table using SQLAlchemy
        UserFavorite.__table__.create(engine, checkfirst=True)
        
        print("✅ user_favorites table created successfully!")
        
        # Check the new structure
        result = db.execute(text("DESCRIBE user_favorites"))
        columns = result.fetchall()
        
        print("user_favorites table structure:")
        for column in columns:
            print(f"  {column}")
            
    except Exception as e:
        print(f"❌ Error creating table: {e}")
        db.rollback()
    finally:
        db.close()

def check_existing_tables():
    """Check what tables currently exist"""
    db = SessionLocal()
    try:
        result = db.execute(text("SHOW TABLES"))
        tables = result.fetchall()
        
        print("Existing tables:")
        for table in tables:
            print(f"  {table[0]}")
            
        return [table[0] for table in tables]
        
    except Exception as e:
        print(f"❌ Error checking tables: {e}")
        return []
    finally:
        db.close()

if __name__ == "__main__":
    print("🔍 Checking existing database tables...")
    
    existing_tables = check_existing_tables()
    
    if 'user_favorites' not in existing_tables:
        print("user_favorites table not found. Creating it...")
        create_favorites_table()
    else:
        print("✅ user_favorites table already exists!")
    
    print("🎉 Database update completed!")
