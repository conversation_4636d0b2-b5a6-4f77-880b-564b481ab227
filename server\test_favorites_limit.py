#!/usr/bin/env python3
"""
Script to test favorites 20-item limit and FIFO behavior
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_favorites_limit():
    """Test the 20-item limit and FIFO behavior"""
    
    # First, let's register/login a test user
    print("1. Testing user registration/login...")
    
    user_data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        # Register or login
        response = requests.post(f"{BASE_URL}/auth/register-or-login", json=user_data)
        if response.status_code == 200:
            auth_data = response.json()
            token = auth_data["access_token"]
            print(f"✅ User authenticated successfully")
            
            headers = {"Authorization": f"Bearer {token}"}
            
            # Get all products
            print("\n2. Getting all products...")
            response = requests.get(f"{BASE_URL}/products", headers=headers)
            if response.status_code == 200:
                products = response.json()
                print(f"✅ Got {len(products)} total products")
                
                # Clear existing favorites first
                print("\n3. Clearing existing favorites...")
                response = requests.get(f"{BASE_URL}/favorites", headers=headers)
                if response.status_code == 200:
                    existing_favorites = response.json()
                    for fav in existing_favorites:
                        favorite_data = {"product_id": fav["product"]["id"]}
                        requests.post(f"{BASE_URL}/favorites/toggle", json=favorite_data, headers=headers)
                    print(f"✅ Cleared {len(existing_favorites)} existing favorites")
                
                # Add products to favorites one by one
                print(f"\n4. Adding products to favorites (testing 20-item limit)...")
                
                # We'll add all available products to test the limit
                for i, product in enumerate(products):
                    favorite_data = {"product_id": product["id"]}
                    response = requests.post(f"{BASE_URL}/favorites/toggle", json=favorite_data, headers=headers)
                    if response.status_code == 200:
                        result = response.json()
                        print(f"  Added #{i+1}: {product['name']} - {result['message']}")
                        
                        # Check current favorites count
                        fav_response = requests.get(f"{BASE_URL}/favorites", headers=headers)
                        if fav_response.status_code == 200:
                            current_favorites = fav_response.json()
                            print(f"    Current favorites count: {len(current_favorites)}")
                            
                            # If we have more than 20, something is wrong
                            if len(current_favorites) > 20:
                                print(f"    ❌ ERROR: More than 20 favorites! ({len(current_favorites)})")
                                break
                    else:
                        print(f"  ❌ Failed to add {product['name']}: {response.status_code}")
                
                # Final check
                print(f"\n5. Final favorites check...")
                response = requests.get(f"{BASE_URL}/favorites", headers=headers)
                if response.status_code == 200:
                    final_favorites = response.json()
                    print(f"✅ Final favorites count: {len(final_favorites)}")
                    print("Final favorites list:")
                    for i, fav in enumerate(final_favorites):
                        print(f"  {i+1}. {fav['product']['name']} at {fav['product']['store']} (added: {fav['created_at']})")
                    
                    if len(final_favorites) <= 20:
                        print("✅ 20-item limit working correctly!")
                    else:
                        print(f"❌ Limit not working: {len(final_favorites)} favorites")
                else:
                    print(f"❌ Failed to get final favorites: {response.status_code}")
                    
            else:
                print(f"❌ Failed to get products: {response.status_code} - {response.text}")
        else:
            print(f"❌ Failed to authenticate: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🧪 Testing favorites 20-item limit and FIFO behavior...")
    test_favorites_limit()
    print("\n🎉 Test completed!")
