#!/usr/bin/env python3
"""
Script to check and fix database structure
"""

from sqlalchemy.orm import Session
from sqlalchemy import text
from database import SessionLocal, engine, Base, Product
import pymysql

def check_table_structure():
    """Check the current table structure"""
    db = SessionLocal()
    try:
        # Check if products table exists and its structure
        result = db.execute(text("DESCRIBE products"))
        columns = result.fetchall()
        
        print("Current products table structure:")
        for column in columns:
            print(f"  {column}")
            
        return columns
        
    except Exception as e:
        print(f"Error checking table structure: {e}")
        return None
    finally:
        db.close()

def recreate_products_table():
    """Drop and recreate the products table with correct structure"""
    db = SessionLocal()
    try:
        print("Dropping existing products table...")
        db.execute(text("DROP TABLE IF EXISTS products"))
        db.commit()
        
        print("Creating products table with correct structure...")
        # Create the table using SQLAlchemy
        Product.__table__.create(engine)
        
        print("✅ Products table recreated successfully!")
        
        # Check the new structure
        result = db.execute(text("DESCRIBE products"))
        columns = result.fetchall()
        
        print("New products table structure:")
        for column in columns:
            print(f"  {column}")
            
    except Exception as e:
        print(f"❌ Error recreating table: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🔍 Checking database structure...")
    
    columns = check_table_structure()
    
    if columns:
        # Check if price column exists
        column_names = [col[0] for col in columns]
        if 'price' not in column_names:
            print("❌ price column missing. Recreating table...")
            recreate_products_table()
        else:
            print("✅ Table structure looks correct!")
    else:
        print("Creating products table...")
        recreate_products_table()
